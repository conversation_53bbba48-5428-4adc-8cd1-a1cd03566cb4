{"version": 3, "file": "AbstractChatCompletionRunner.js", "sourceRoot": "", "sources": ["../src/lib/AbstractChatCompletionRunner.ts"], "names": [], "mappings": ";;;;;;;;;AASA,uCAAuC;AACvC,4DAK4B;AAM5B,kEAA6F;AAC7F,kDAAwD;AAGxD,6CAAwE;AAExE,MAAM,4BAA4B,GAAG,EAAE,CAAC;AAMxC,MAAa,4BAGX,SAAQ,yBAAuB;IAHjC;;;QAIY,qBAAgB,GAAoC,EAAE,CAAC;QACjE,aAAQ,GAAiC,EAAE,CAAC;IAmc9C,CAAC;IAjcW,kBAAkB,CAE1B,cAA6C;QAE7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;QACnD,IAAI,OAAO;YAAE,IAAI,CAAC,WAAW,CAAC,OAAqC,CAAC,CAAC;QACrE,OAAO,cAAc,CAAC;IACxB,CAAC;IAES,WAAW,CAEnB,OAAmC,EACnC,IAAI,GAAG,IAAI;QAEX,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,CAAC;YAAE,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5B,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC/B,IAAI,CAAC,IAAA,uCAAiB,EAAC,OAAO,CAAC,IAAI,IAAA,mCAAa,EAAC,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE;gBAC7E,8GAA8G;gBAC9G,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,OAAO,CAAC,OAAiB,CAAC,CAAC;aAC7D;iBAAM,IAAI,IAAA,wCAAkB,EAAC,OAAO,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE;gBAC/D,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;aACnD;iBAAM,IAAI,IAAA,wCAAkB,EAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE;gBAC5D,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,UAAU,EAAE;oBAC1C,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE;wBACjC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;qBAChD;iBACF;aACF;SACF;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,mBAAW,CAAC,iDAAiD,CAAC,CAAC;QAC1F,OAAO,UAAU,CAAC;IACpB,CAAC;IAMD;;;OAGG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,8FAAiB,MAArB,IAAI,CAAmB,CAAC;IACjC,CAAC;IAwBD;;;OAGG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,8FAAiB,MAArB,IAAI,CAAmB,CAAC;IACjC,CAAC;IAgBD;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACrB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,mGAAsB,MAA1B,IAAI,CAAwB,CAAC;IACtC,CAAC;IAyBD,KAAK,CAAC,uBAAuB;QAC3B,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,yGAA4B,MAAhC,IAAI,CAA8B,CAAC;IAC5C,CAAC;IAkBD,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,uBAAA,IAAI,kGAAqB,MAAzB,IAAI,CAAuB,CAAC;IACrC,CAAC;IAED,kBAAkB;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACpC,CAAC;IAEkB,UAAU;QAG3B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,UAAU;YAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC;QAC9D,MAAM,YAAY,GAAG,uBAAA,IAAI,8FAAiB,MAArB,IAAI,CAAmB,CAAC;QAC7C,IAAI,YAAY;YAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAG,uBAAA,IAAI,8FAAiB,MAArB,IAAI,CAAmB,CAAC;QAC7C,IAAI,YAAY;YAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAE3D,MAAM,iBAAiB,GAAG,uBAAA,IAAI,mGAAsB,MAA1B,IAAI,CAAwB,CAAC;QACvD,IAAI,iBAAiB;YAAE,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;QAE1E,MAAM,uBAAuB,GAAG,uBAAA,IAAI,yGAA4B,MAAhC,IAAI,CAA8B,CAAC;QACnE,IAAI,uBAAuB,IAAI,IAAI;YAAE,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,uBAAuB,CAAC,CAAC;QAEpG,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,uBAAA,IAAI,kGAAqB,MAAzB,IAAI,CAAuB,CAAC,CAAC;SACvD;IACH,CAAC;IAUS,KAAK,CAAC,qBAAqB,CACnC,MAAc,EACd,MAAkC,EAClC,OAA6B;QAE7B,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,CAAC;QAC/B,IAAI,MAAM,EAAE;YACV,IAAI,MAAM,CAAC,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;SACjE;QACD,uBAAA,IAAI,6FAAgB,MAApB,IAAI,EAAiB,MAAM,CAAC,CAAC;QAE7B,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CACzD,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,EAC5B,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAC/C,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAA,4BAAmB,EAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9E,CAAC;IAES,KAAK,CAAC,kBAAkB,CAChC,MAAc,EACd,MAAkC,EAClC,OAA6B;QAE7B,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE;YACrC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAClC;QACD,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;IAES,KAAK,CAAC,aAAa,CAC3B,MAAc,EACd,MAE8D,EAC9D,OAAuB;QAEvB,MAAM,IAAI,GAAG,UAAmB,CAAC;QACjC,MAAM,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,EAAE,GAAG,MAAM,CAAC;QACjE,MAAM,oBAAoB,GAAG,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,EAAE,IAAI,CAAC;QACtF,MAAM,EAAE,kBAAkB,GAAG,4BAA4B,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAE5E,MAAM,eAAe,GAA0C,EAAE,CAAC;QAClE,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE;YAChC,eAAe,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAChD;QAED,MAAM,SAAS,GAA0C,MAAM,CAAC,SAAS,CAAC,GAAG,CAC3E,CAAC,CAAC,EAAuC,EAAE,CAAC,CAAC;YAC3C,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI;YAC/B,UAAU,EAAE,CAAC,CAAC,UAAqC;YACnD,WAAW,EAAE,CAAC,CAAC,WAAW;SAC3B,CAAC,CACH,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE;YACrC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAClC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,EAAE,CAAC,EAAE;YAC3C,MAAM,cAAc,GAAmB,MAAM,IAAI,CAAC,qBAAqB,CACrE,MAAM,EACN;gBACE,GAAG,UAAU;gBACb,aAAa;gBACb,SAAS;gBACT,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC7B,EACD,OAAO,CACR,CAAC;YACF,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;YACnD,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,mBAAW,CAAC,4CAA4C,CAAC,CAAC;aACrE;YACD,IAAI,CAAC,OAAO,CAAC,aAAa;gBAAE,OAAO;YACnC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC;YACxD,MAAM,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,CAAC,EAAE,EAAE;gBACP,MAAM,OAAO,GAAG,0BAA0B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,4BAA4B,SAAS;qBAChG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;qBAClC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;gBAElC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC1C,SAAS;aACV;iBAAM,IAAI,oBAAoB,IAAI,oBAAoB,KAAK,IAAI,EAAE;gBAChE,MAAM,OAAO,GAAG,0BAA0B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAC/E,oBAAoB,CACrB,8BAA8B,CAAC;gBAEhC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC1C,SAAS;aACV;YAED,IAAI,MAAM,CAAC;YACX,IAAI;gBACF,MAAM,GAAG,IAAA,8CAA2B,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;aACxE;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,WAAW,CAAC;oBACf,IAAI;oBACJ,IAAI;oBACJ,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAChE,CAAC,CAAC;gBACH,SAAS;aACV;YAED,mDAAmD;YACnD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,uBAAA,IAAI,0GAA6B,MAAjC,IAAI,EAA8B,UAAU,CAAC,CAAC;YAE9D,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;YAE1C,IAAI,oBAAoB;gBAAE,OAAO;SAClC;IACH,CAAC;IAES,KAAK,CAAC,SAAS,CACvB,MAAc,EACd,MAE0D,EAC1D,OAAuB;QAEvB,MAAM,IAAI,GAAG,MAAe,CAAC;QAC7B,MAAM,EAAE,WAAW,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,EAAE,GAAG,MAAM,CAAC;QAC/D,MAAM,oBAAoB,GAAG,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC;QAC5F,MAAM,EAAE,kBAAkB,GAAG,4BAA4B,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAE5E,qCAAqC;QACrC,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAA6B,EAAE;YACtE,IAAI,IAAA,2BAAkB,EAAC,IAAI,CAAC,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,MAAM,IAAI,mBAAW,CAAC,uEAAuE,CAAC,CAAC;iBAChG;gBAED,OAAO;oBACL,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,QAAQ,EAAE,IAAI,CAAC,SAAS;wBACxB,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;wBACxB,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE;wBAC5C,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAiB;wBAC3C,KAAK,EAAE,IAAI,CAAC,SAAS;wBACrB,MAAM,EAAE,IAAI;qBACb;iBACF,CAAC;aACH;YAED,OAAO,IAAwC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,MAAM,eAAe,GAA0C,EAAE,CAAC;QAClE,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;YAC1B,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;gBACzB,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;aAC3E;SACF;QAED,MAAM,KAAK,GACT,OAAO,IAAI,MAAM,CAAC,CAAC;YACjB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACnB,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;gBACrB;oBACE,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI;wBACjD,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAqC;wBAC5D,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW;wBACnC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM;qBAC1B;iBACF;gBACH,CAAC,CAAE,CAAmC,CACvC;YACH,CAAC,CAAE,SAAiB,CAAC;QAEvB,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE;YACrC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SAClC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,EAAE,CAAC,EAAE;YAC3C,MAAM,cAAc,GAAmB,MAAM,IAAI,CAAC,qBAAqB,CACrE,MAAM,EACN;gBACE,GAAG,UAAU;gBACb,WAAW;gBACX,KAAK;gBACL,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;aAC7B,EACD,OAAO,CACR,CAAC;YACF,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;YACnD,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,mBAAW,CAAC,4CAA4C,CAAC,CAAC;aACrE;YACD,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE;gBAC/B,OAAO;aACR;YAED,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,UAAU,EAAE;gBAC1C,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU;oBAAE,SAAS;gBAC5C,MAAM,YAAY,GAAG,SAAS,CAAC,EAAE,CAAC;gBAClC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC;gBACrD,MAAM,EAAE,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;gBAEjC,IAAI,CAAC,EAAE,EAAE;oBACP,MAAM,OAAO,GAAG,sBAAsB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,4BAA4B,MAAM,CAAC,IAAI,CAC/F,eAAe,CAChB;yBACE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;oBAElC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;oBAClD,SAAS;iBACV;qBAAM,IAAI,oBAAoB,IAAI,oBAAoB,KAAK,IAAI,EAAE;oBAChE,MAAM,OAAO,GAAG,sBAAsB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAC3E,oBAAoB,CACrB,8BAA8B,CAAC;oBAEhC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;oBAClD,SAAS;iBACV;gBAED,IAAI,MAAM,CAAC;gBACX,IAAI;oBACF,MAAM,GAAG,IAAA,8CAA2B,EAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;iBACxE;gBAAC,OAAO,KAAK,EAAE;oBACd,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACvE,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;oBAClD,SAAS;iBACV;gBAED,mDAAmD;gBACnD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBACnD,MAAM,OAAO,GAAG,uBAAA,IAAI,0GAA6B,MAAjC,IAAI,EAA8B,UAAU,CAAC,CAAC;gBAC9D,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;gBAElD,IAAI,oBAAoB,EAAE;oBACxB,OAAO;iBACR;aACF;SACF;QAED,OAAO;IACT,CAAC;CASF;AAxcD,oEAwcC;;IAhZG,OAAO,uBAAA,IAAI,8FAAiB,MAArB,IAAI,CAAmB,CAAC,OAAO,IAAI,IAAI,CAAC;AACjD,CAAC;IAYC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC7B,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,IAAA,wCAAkB,EAAC,OAAO,CAAC,EAAE;YAC/B,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;YAE3C,2BAA2B;YAC3B,MAAM,GAAG,GAAyC;gBAChD,GAAG,IAAI;gBACP,OAAO,EAAG,OAAiC,CAAC,OAAO,IAAI,IAAI;gBAC3D,OAAO,EAAG,OAAiC,CAAC,OAAO,IAAI,IAAI;aAC5D,CAAC;YACF,IAAI,aAAa,EAAE;gBACjB,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC;aACnC;YACD,OAAO,GAAG,CAAC;SACZ;KACF;IACD,MAAM,IAAI,mBAAW,CAAC,4EAA4E,CAAC,CAAC;AACtG,CAAC;IAYC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,IAAA,wCAAkB,EAAC,OAAO,CAAC,IAAI,OAAO,EAAE,aAAa,EAAE;YACzD,OAAO,OAAO,CAAC,aAAa,CAAC;SAC9B;QACD,IAAI,IAAA,wCAAkB,EAAC,OAAO,CAAC,IAAI,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;YAC9D,OAAO,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;SAC5C;KACF;IAED,OAAO;AACT,CAAC;IAYC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,IAAA,uCAAiB,EAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;YACzD,OAAO,OAAO,CAAC,OAAO,CAAC;SACxB;QACD,IACE,IAAA,mCAAa,EAAC,OAAO,CAAC;YACtB,OAAO,CAAC,OAAO,IAAI,IAAI;YACvB,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ;YACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAChB,CAAC,CAAC,EAAE,EAAE,CACJ,CAAC,CAAC,IAAI,KAAK,WAAW;gBACtB,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC,CACpF,EACD;YACA,OAAO,OAAO,CAAC,OAAO,CAAC;SACxB;KACF;IAED,OAAO;AACT,CAAC;IAQC,MAAM,KAAK,GAAoB;QAC7B,iBAAiB,EAAE,CAAC;QACpB,aAAa,EAAE,CAAC;QAChB,YAAY,EAAE,CAAC;KAChB,CAAC;IACF,KAAK,MAAM,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;QAC7C,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,iBAAiB,CAAC;YACnD,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC;YAC3C,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC;SAC1C;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC,uGAgCe,MAAkC;IAChD,IAAI,MAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;QACpC,MAAM,IAAI,mBAAW,CACnB,8HAA8H,CAC/H,CAAC;KACH;AACH,CAAC,iIAuP4B,UAAmB;IAC9C,OAAO,CACL,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU;QAC3C,CAAC,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW;YACxC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAC7B,CAAC;AACJ,CAAC"}