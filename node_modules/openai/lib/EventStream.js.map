{"version": 3, "file": "EventStream.js", "sourceRoot": "", "sources": ["../src/lib/EventStream.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,uCAA0D;AAE1D,MAAa,WAAW;IAoBtB;;QAnBA,eAAU,GAAoB,IAAI,eAAe,EAAE,CAAC;QAEpD,gDAAiC;QACjC,+CAAuC,GAAG,EAAE,GAAE,CAAC,EAAC;QAChD,8CAAwD,GAAG,EAAE,GAAE,CAAC,EAAC;QAEjE,0CAA2B;QAC3B,yCAAiC,GAAG,EAAE,GAAE,CAAC,EAAC;QAC1C,wCAAkD,GAAG,EAAE,GAAE,CAAC,EAAC;QAE3D,iCAEI,EAAE,EAAC;QAEP,6BAAS,KAAK,EAAC;QACf,+BAAW,KAAK,EAAC;QACjB,+BAAW,KAAK,EAAC;QACjB,8CAA0B,KAAK,EAAC;QAG9B,uBAAA,IAAI,iCAAqB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7D,uBAAA,IAAI,wCAA4B,OAAO,MAAA,CAAC;YACxC,uBAAA,IAAI,uCAA2B,MAAM,MAAA,CAAC;QACxC,CAAC,CAAC,MAAA,CAAC;QAEH,uBAAA,IAAI,2BAAe,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACvD,uBAAA,IAAI,kCAAsB,OAAO,MAAA,CAAC;YAClC,uBAAA,IAAI,iCAAqB,MAAM,MAAA,CAAC;QAClC,CAAC,CAAC,MAAA,CAAC;QAEH,6DAA6D;QAC7D,4DAA4D;QAC5D,6DAA6D;QAC7D,gCAAgC;QAChC,uBAAA,IAAI,qCAAkB,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACvC,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAES,IAAI,CAAgC,QAA4B;QACxE,gFAAgF;QAChF,sEAAsE;QACtE,UAAU,CAAC,GAAG,EAAE;YACd,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACnB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC,EAAE,uBAAA,IAAI,wDAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAES,UAAU;QAClB,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,uBAAA,IAAI,4CAAyB,MAA7B,IAAI,CAA2B,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACxB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,uBAAA,IAAI,0BAAO,CAAC;IACrB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,4BAAS,CAAC;IACvB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,uBAAA,IAAI,4BAAS,CAAC;IACvB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;OAMG;IACH,EAAE,CAAiC,KAAY,EAAE,QAA0C;QACzF,MAAM,SAAS,GACb,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAiC,KAAY,EAAE,QAA0C;QAC1F,MAAM,SAAS,GAAG,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAC5B,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAClE,IAAI,KAAK,IAAI,CAAC;YAAE,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAiC,KAAY,EAAE,QAA0C;QAC3F,MAAM,SAAS,GACb,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1D,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;OAUG;IACH,OAAO,CACL,KAAY;QAMZ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,uBAAA,IAAI,uCAA2B,IAAI,MAAA,CAAC;YACpC,IAAI,KAAK,KAAK,OAAO;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAc,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,uBAAA,IAAI,uCAA2B,IAAI,MAAA,CAAC;QACpC,MAAM,uBAAA,IAAI,+BAAY,CAAC;IACzB,CAAC;IAyBD,KAAK,CAEH,KAAY,EACZ,GAAG,IAAwC;QAE3C,+CAA+C;QAC/C,IAAI,uBAAA,IAAI,0BAAO,EAAE;YACf,OAAO;SACR;QAED,IAAI,KAAK,KAAK,KAAK,EAAE;YACnB,uBAAA,IAAI,sBAAU,IAAI,MAAA,CAAC;YACnB,uBAAA,IAAI,sCAAmB,MAAvB,IAAI,CAAqB,CAAC;SAC3B;QAED,MAAM,SAAS,GAAkD,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,CAAC;QACxF,IAAI,SAAS,EAAE;YACb,uBAAA,IAAI,8BAAW,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAQ,CAAC;YACjE,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAI,IAAY,CAAC,CAAC,CAAC;SACtE;QAED,IAAI,KAAK,KAAK,OAAO,EAAE;YACrB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAsB,CAAC;YAC3C,IAAI,CAAC,uBAAA,IAAI,2CAAwB,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;YACD,uBAAA,IAAI,2CAAwB,MAA5B,IAAI,EAAyB,KAAK,CAAC,CAAC;YACpC,uBAAA,IAAI,qCAAkB,MAAtB,IAAI,EAAmB,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAClB,OAAO;SACR;QAED,IAAI,KAAK,KAAK,OAAO,EAAE;YACrB,yEAAyE;YAEzE,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAgB,CAAC;YACrC,IAAI,CAAC,uBAAA,IAAI,2CAAwB,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;gBACvD,mFAAmF;gBACnF,8EAA8E;gBAC9E,kCAAkC;gBAClC,wBAAwB;gBACxB,uCAAuC;gBACvC,SAAS;gBACT,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;YACD,uBAAA,IAAI,2CAAwB,MAA5B,IAAI,EAAyB,KAAK,CAAC,CAAC;YACpC,uBAAA,IAAI,qCAAkB,MAAtB,IAAI,EAAmB,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACnB;IACH,CAAC;IAES,UAAU,KAAU,CAAC;CAChC;AA1ND,kCA0NC;olBA3E6C,KAAc;IACxD,uBAAA,IAAI,wBAAY,IAAI,MAAA,CAAC;IACrB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;QACzD,KAAK,GAAG,IAAI,yBAAiB,EAAE,CAAC;KACjC;IACD,IAAI,KAAK,YAAY,yBAAiB,EAAE;QACtC,uBAAA,IAAI,wBAAY,IAAI,MAAA,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACnC;IACD,IAAI,KAAK,YAAY,mBAAW,EAAE;QAChC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KACnC;IACD,IAAI,KAAK,YAAY,KAAK,EAAE;QAC1B,MAAM,WAAW,GAAgB,IAAI,mBAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChE,aAAa;QACb,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;KACzC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,mBAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC"}