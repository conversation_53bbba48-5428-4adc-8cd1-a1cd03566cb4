"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isPresent = exports.isToolMessage = exports.isFunctionMessage = exports.isAssistantMessage = void 0;
const isAssistantMessage = (message) => {
    return message?.role === 'assistant';
};
exports.isAssistantMessage = isAssistantMessage;
const isFunctionMessage = (message) => {
    return message?.role === 'function';
};
exports.isFunctionMessage = isFunctionMessage;
const isToolMessage = (message) => {
    return message?.role === 'tool';
};
exports.isToolMessage = isToolMessage;
function isPresent(obj) {
    return obj != null;
}
exports.isPresent = isPresent;
//# sourceMappingURL=chatCompletionUtils.js.map