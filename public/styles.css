/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background: #1a1a1a;
    min-height: 100vh;
    color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
    position: relative;
}

/* Header */
.header {
    padding: 60px 20px 20px 20px;
    text-align: left;
}

.header h1 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header h1 i {
    color: #00d4aa;
    font-size: 1rem;
}

.subtitle {
    font-size: 0.9rem;
    color: #888;
    display: none; /* Hide subtitle in mobile design */
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
}

/* Voice Interface */
.voice-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: transparent;
}

.conversation-display {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
    background: transparent;
    margin-bottom: 20px;
}

.welcome-message {
    margin-bottom: 30px;
}

.welcome-message p {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
    line-height: 1.3;
    margin-bottom: 15px;
}

.welcome-message i {
    display: none;
}

.message {
    margin-bottom: 20px;
    font-size: 1.1rem;
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message {
    color: #ffffff;
    text-align: left;
    font-weight: 500;
}

.ai-message {
    color: #ffffff;
    font-size: 1.8rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 30px;
}

.play-audio-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #00d4aa;
    color: #1a1a1a;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.play-audio-btn:hover {
    background: #00b894;
    transform: scale(1.05);
}

.message-content {
    display: flex;
    flex-direction: column;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    margin: 15px auto;
    max-width: 90%;
}

.error-content strong {
    display: block;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.error-content details {
    margin-top: 10px;
    padding: 8px;
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

.error-content summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 5px;
}

.error-content pre {
    font-size: 0.85em;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 5px 0 0 0;
}

/* Voice Controls */
.voice-controls {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.mic-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: #333;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.mic-button:hover {
    background: #444;
    transform: scale(1.05);
}

.mic-button.recording {
    background: #00d4aa;
    animation: pulse 1.5s infinite;
}

.mic-button.listening {
    background: #007acc;
    animation: listening-pulse 2s infinite;
}

.mic-status {
    display: none; /* Hide status text in mobile design */
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes listening-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 122, 204, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 10px rgba(0, 122, 204, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 0 0 rgba(0, 122, 204, 0);
    }
}

/* Stop Button */
.stop-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: #ff4757;
    color: #ffffff;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.stop-button:hover {
    background: #ff3742;
    transform: scale(1.05);
}

.stop-text {
    font-size: 0.7rem;
    margin-top: 2px;
    font-weight: 500;
}

/* AI Speaking Indicator */
.ai-speaking-indicator {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 212, 170, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 170, 0.3);
    border-radius: 25px;
    padding: 15px 25px;
    display: flex;
    align-items: center;
    gap: 15px;
    animation: fadeInUp 0.3s ease;
}

.speaking-animation {
    display: flex;
    align-items: center;
    gap: 3px;
}

.wave {
    width: 3px;
    height: 20px;
    background: #00d4aa;
    border-radius: 2px;
    animation: wave 1.2s ease-in-out infinite;
}

.wave:nth-child(1) { animation-delay: 0s; }
.wave:nth-child(2) { animation-delay: 0.1s; }
.wave:nth-child(3) { animation-delay: 0.2s; }
.wave:nth-child(4) { animation-delay: 0.3s; }
.wave:nth-child(5) { animation-delay: 0.4s; }

@keyframes wave {
    0%, 40%, 100% {
        transform: scaleY(0.4);
        opacity: 0.5;
    }
    20% {
        transform: scaleY(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.speaking-text {
    color: #00d4aa;
    font-size: 0.9rem;
    font-weight: 500;
}

.audio-controls {
    display: none; /* Hide audio controls in mobile design */
}

.control-btn {
    display: none;
}

/* Status Indicator */
.status-indicator {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    max-width: 280px;
    opacity: 1;
}

.status-indicator.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
    pointer-events: none;
}

.status-text {
    font-size: 0.8rem;
    color: #00d4aa;
    font-weight: 500;
}

/* Lead Panel - Hidden in mobile design */
.lead-panel {
    display: none;
}

/* Footer - Hidden in mobile design */
.footer {
    display: none;
}

/* Progress indicator */
.progress-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 212, 170, 0.2);
    color: #00d4aa;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: none;
}

.progress-indicator.show {
    display: block;
}

/* Responsive Design - Already mobile-first */
@media (min-width: 768px) {
    .container {
        max-width: 400px;
    }
}
