class VoiceLeadApp {
    constructor() {
        this.isRecording = false;
        this.isListening = false; // New: tracks if voice mode is active
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.currentAudio = null;
        this.leadData = {};
        this.conversationHistory = [];
        this.isFirstMessage = true;
        this.userHasInteracted = false;
        this.statusHideTimer = null;
        this.sessionId = Date.now().toString(); // Unique session ID
        this.voiceActivationTimeout = null; // For automatic recording restart

        this.initializeElements();
        this.bindEvents();
        this.checkMicrophoneSupport();
        this.checkAudioFormats();
        this.showWelcomeMessage();
        this.startStatusHideTimer();
    }

    initializeElements() {
        this.micButton = document.getElementById('micButton');
        this.micIcon = document.getElementById('micIcon');
        this.micStatus = document.getElementById('micStatus');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.conversationDisplay = document.getElementById('conversationDisplay');
        this.playButton = document.getElementById('playButton');
        this.stopButton = document.getElementById('stopButton');
        this.oldStopButton = document.getElementById('oldStopButton');
        this.audioPlayer = document.getElementById('audioPlayer');
        this.saveLeadBtn = document.getElementById('saveLeadBtn');
        this.exportLeadBtn = document.getElementById('exportLeadBtn');
        this.leadInfo = document.getElementById('leadInfo');
        this.aiSpeakingIndicator = document.getElementById('aiSpeakingIndicator');
    }

    bindEvents() {
        this.micButton.addEventListener('click', () => this.toggleRecording());
        this.playButton.addEventListener('click', () => this.playLastResponse());
        this.stopButton.addEventListener('click', () => this.stopEverything());
        this.oldStopButton.addEventListener('click', () => this.stopAudio());
        this.saveLeadBtn.addEventListener('click', () => this.saveLead());
        this.exportLeadBtn.addEventListener('click', () => this.exportLead());
    }

    checkAudioFormats() {
        console.log('=== Audio Format Support Check ===');
        const formats = [
            'audio/webm',
            'audio/webm;codecs=opus',
            'audio/mp4',
            'audio/ogg',
            'audio/wav',
            'audio/mpeg'
        ];

        formats.forEach(format => {
            const supported = MediaRecorder.isTypeSupported(format);
            console.log(`${format}: ${supported ? '✓ Supported' : '✗ Not supported'}`);
        });
        console.log('=== End Audio Format Check ===');

        // Test basic microphone access
        this.testMicrophoneAccess();
    }

    async testMicrophoneAccess() {
        console.log('=== Testing Microphone Access ===');
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            console.log('✅ Microphone access: GRANTED');
            console.log('📱 Audio tracks:', stream.getAudioTracks().length);
            stream.getAudioTracks().forEach((track, index) => {
                console.log(`🎤 Track ${index}:`, track.label, 'State:', track.readyState);
            });
            stream.getTracks().forEach(track => track.stop());
        } catch (error) {
            console.error('❌ Microphone access: DENIED');
            console.error('Error:', error.name, error.message);
        }
        console.log('=== End Microphone Test ===');
    }

    async checkMicrophoneSupport() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('Microphone ready - click to start');
        } catch (error) {
            this.micButton.disabled = true;
            console.error('Microphone error:', error);

            let errorMessage = 'Microphone access is required for voice functionality.';
            let details = error.message;

            if (error.name === 'NotAllowedError') {
                errorMessage = 'Microphone access was denied. Please allow microphone access and refresh the page.';
                details = 'Click the microphone icon in your browser\'s address bar to allow access.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No microphone found. Please connect a microphone and refresh the page.';
            }

            this.showError('Microphone Not Available', errorMessage, details);
        }
    }

    startStatusHideTimer() {
        // Hide status indicator after 5 seconds
        this.statusHideTimer = setTimeout(() => {
            this.statusIndicator.classList.add('hidden');
        }, 5000);
    }

    showWelcomeMessage() {
        // Show initial AI greeting (but don't auto-play audio)
        setTimeout(() => {
            const welcomeText = "Hello, James. The Market has been noisy - lets catch you up.";
            this.addMessage(welcomeText, 'ai');
            // Don't auto-play the welcome message - wait for user interaction
        }, 1000);
    }

    async toggleRecording() {
        console.log('🔘 Microphone button clicked!');
        console.log('🔘 Current state - isListening:', this.isListening, 'isRecording:', this.isRecording);

        // Mark that user has interacted with the page
        this.userHasInteracted = true;

        if (this.isListening) {
            // Turn OFF voice mode
            console.log('🔘 Turning OFF voice mode...');
            this.stopVoiceMode();
        } else {
            // Turn ON voice mode
            console.log('🔘 Turning ON voice mode...');
            await this.startVoiceMode();
        }
    }

    async startVoiceMode() {
        console.log('🎤 Starting voice mode...');
        this.isListening = true;
        this.updateUI('listening');
        this.updateStatus('Voice mode ON - Listening for speech...');

        // Add a big visual indicator
        this.addMessage('🎤 VOICE MODE ACTIVATED - Speak now!', 'system');

        // Start the first recording session
        console.log('🎤 Starting first recording session...');
        await this.startRecording();
    }

    stopVoiceMode() {
        console.log('🛑 Stopping voice mode...');
        this.isListening = false;

        // Clear any pending restart timeout
        if (this.voiceActivationTimeout) {
            clearTimeout(this.voiceActivationTimeout);
            this.voiceActivationTimeout = null;
        }

        // Stop current recording if active
        if (this.isRecording) {
            console.log('🛑 Stopping active recording...');
            this.stopRecording();
        }

        // Add visual indicator
        this.addMessage('🛑 VOICE MODE DEACTIVATED', 'system');

        this.updateUI('ready');
        this.updateStatus('Voice mode OFF - Click to activate');
    }

    async startRecording() {
        console.log('🎙️ Starting recording...');
        try {
            console.log('🎙️ Requesting microphone access...');
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 44100, // Changed to more compatible sample rate
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });
            console.log('🎙️ Microphone access granted!');

            // Try to use a supported audio format with better compatibility
            let options = {};
            let mimeType = '';

            // Check for supported formats in order of preference
            if (MediaRecorder.isTypeSupported('audio/webm')) {
                options = { mimeType: 'audio/webm' };
                mimeType = 'audio/webm';
            } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
                options = { mimeType: 'audio/mp4' };
                mimeType = 'audio/mp4';
            } else if (MediaRecorder.isTypeSupported('audio/ogg')) {
                options = { mimeType: 'audio/ogg' };
                mimeType = 'audio/ogg';
            } else {
                // Fallback to default (usually works)
                console.log('Using default MediaRecorder format');
                mimeType = 'audio/wav'; // Default assumption
            }

            console.log('Using audio format:', mimeType);

            this.mediaRecorder = new MediaRecorder(stream, options);
            this.audioChunks = [];
            this.recordedMimeType = mimeType; // Store for later use

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start(1000); // Record in 1-second chunks
            this.isRecording = true;

            console.log('🎙️ Recording started successfully!');
            console.log('🎙️ MediaRecorder state:', this.mediaRecorder.state);

            this.updateUI('recording');
            this.updateStatus('Listening... Click again to stop');

        } catch (error) {
            console.error('Recording error:', error);
            this.showError(
                'Recording Failed',
                'Could not start recording. Please check microphone permissions.',
                `Error: ${error.message}\nMake sure your browser has microphone access.`
            );

            // If in listening mode, try to restart after error
            if (this.isListening) {
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        console.log('Retrying recording after error...');
                        await this.startRecording();
                    }
                }, 5000); // 5 second delay before retry
            }
        }
    }

    stopRecording() {
        console.log('🛑 Stopping recording...');
        if (this.mediaRecorder && this.isRecording) {
            console.log('🛑 MediaRecorder state before stop:', this.mediaRecorder.state);
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;

            console.log('🛑 Recording stopped, processing...');
            this.updateUI('processing');
            this.updateStatus('Processing your message...');
        } else {
            console.log('🛑 No active recording to stop');
        }
    }

    async processRecording() {
        try {
            // Check if we have audio data
            if (this.audioChunks.length === 0) {
                throw new Error('No audio data recorded');
            }

            // Determine the correct MIME type based on what was recorded
            let mimeType = this.recordedMimeType || 'audio/wav';
            if (this.mediaRecorder && this.mediaRecorder.mimeType) {
                mimeType = this.mediaRecorder.mimeType;
            }

            console.log('Processing audio with MIME type:', mimeType);
            console.log('Audio chunks:', this.audioChunks.length);

            const audioBlob = new Blob(this.audioChunks, { type: mimeType });

            // Check blob size
            if (audioBlob.size === 0) {
                throw new Error('Audio blob is empty');
            }

            console.log('Audio blob size:', audioBlob.size, 'bytes');

            // Send to speech-to-text API
            const transcription = await this.speechToText(audioBlob);

            if (transcription) {
                this.addMessage(transcription, 'user');

                // Send to AI for response
                const aiResponse = await this.getAIResponse(transcription);

                if (aiResponse) {
                    this.addMessage(aiResponse, 'ai');

                    // Convert AI response to speech
                    await this.textToSpeech(aiResponse);

                    // Extract lead information
                    this.extractLeadInfo(transcription, aiResponse);
                }
            }

            // If still in listening mode, restart recording after a brief pause
            if (this.isListening) {
                this.updateUI('listening');
                this.updateStatus('Voice mode ON - Listening for speech...');

                // Wait a moment before restarting recording to allow for AI response
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        await this.startRecording();
                    }
                }, 2000); // 2 second delay
            } else {
                this.updateUI('ready');
                this.updateStatus('Ready for next message');
            }

        } catch (error) {
            console.error('Processing error:', error);
            this.showError(
                'Processing Failed',
                'Could not process your voice message. Please try again.',
                `Error: ${error.message}\nCheck your microphone and internet connection.`
            );

            // If still in listening mode, try to restart recording
            if (this.isListening) {
                this.updateUI('listening');
                this.voiceActivationTimeout = setTimeout(async () => {
                    if (this.isListening && !this.isRecording) {
                        await this.startRecording();
                    }
                }, 3000); // 3 second delay after error
            } else {
                this.updateUI('ready');
            }
        }
    }

    async speechToText(audioBlob) {
        try {
            const formData = new FormData();

            // Determine file extension based on MIME type
            let filename = 'recording.wav';
            let mimeType = audioBlob.type;

            console.log('Audio blob MIME type:', mimeType);

            if (mimeType.includes('webm')) {
                filename = 'recording.webm';
            } else if (mimeType.includes('mp4')) {
                filename = 'recording.mp4';
            } else if (mimeType.includes('ogg')) {
                filename = 'recording.ogg';
            } else if (mimeType.includes('mpeg')) {
                filename = 'recording.mp3';
            } else {
                // Default to wav for unknown types
                filename = 'recording.wav';
            }

            console.log('Sending audio file:', filename, 'Size:', audioBlob.size);
            formData.append('audio', audioBlob, filename);

            const response = await fetch('/api/speech-to-text', {
                method: 'POST',
                body: formData
            });

            console.log('Speech-to-text response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Speech-to-text HTTP error:', errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Speech-to-text result:', result);

            if (result.success && result.transcription) {
                console.log('Transcription successful:', result.transcription);
                return result.transcription;
            } else {
                throw new Error(result.error || result.details || 'Failed to transcribe audio');
            }

        } catch (error) {
            console.error('Speech-to-text error:', error);
            this.showError(
                'Speech Recognition Failed',
                'Could not convert your speech to text. Please try again.',
                `Error: ${error.message}\nThis might be due to audio format issues or network problems.`
            );
            return null;
        }
    }

    async getAIResponse(message) {
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message,
                    context: 'stock_business_lead',
                    leadData: this.leadData,
                    conversationHistory: this.conversationHistory,
                    sessionId: this.sessionId
                })
            });

            if (!response.ok) {
                console.error('AI response error:', response);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.response) {
                // Add to conversation history
                this.conversationHistory.push(
                    { role: 'user', content: message },
                    { role: 'assistant', content: result.response }
                );

                // Keep conversation history manageable (last 10 exchanges)
                if (this.conversationHistory.length > 20) {
                    this.conversationHistory = this.conversationHistory.slice(-20);
                }

                return result.response;
            } else {
                throw new Error(result.error || 'Failed to get AI response');
            }

        } catch (error) {
            console.error('AI response error:', error);
            this.showError(
                'AI Response Failed',
                'Could not get a response from the AI. Please try again.',
                `Error: ${error.message}\nThis might be due to API issues or network problems.`
            );
            return 'Sorry, I encountered an error. Please try again.';
        }
    }

    async textToSpeech(text) {
        try {
            const response = await fetch('/api/text-to-speech', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ text })
            });

            if (response.ok) {
                const audioBlob = await response.blob();
                const audioUrl = URL.createObjectURL(audioBlob);

                this.currentAudio = audioUrl;
                this.audioPlayer.src = audioUrl;
                this.playButton.disabled = false;
                this.stopButton.disabled = false;

                // Auto-play the response only if user has interacted
                if (this.userHasInteracted) {
                    try {
                        this.updateUI('speaking');
                        await this.audioPlayer.play();
                    } catch (error) {
                        console.log('Auto-play prevented by browser - user can manually play');
                        // Show a visual indicator that audio is ready
                        this.updateStatus('Audio ready - click to play', false);
                        this.updateUI('ready');
                    }
                }

                // Handle audio events
                this.audioPlayer.addEventListener('ended', () => {
                    if (this.isListening) {
                        // If in listening mode, restart recording after AI finishes speaking
                        this.updateUI('listening');
                        this.voiceActivationTimeout = setTimeout(async () => {
                            if (this.isListening && !this.isRecording) {
                                await this.startRecording();
                            }
                        }, 1000); // 1 second delay after AI finishes
                    } else {
                        this.updateUI('ready');
                    }
                });

                this.audioPlayer.addEventListener('pause', () => {
                    if (this.isListening) {
                        this.updateUI('listening');
                    } else {
                        this.updateUI('ready');
                    }
                });

            } else {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

        } catch (error) {
            console.error('Text-to-speech error:', error);
            this.showError(
                'Speech Generation Failed',
                'Could not convert the AI response to speech, but you can still read the text.',
                `Error: ${error.message}\nThe conversation can continue without audio.`
            );
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        if (sender === 'ai' && this.currentAudio && !this.userHasInteracted) {
            // Add play button for AI messages when auto-play is blocked
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${text}
                    <button class="play-audio-btn" onclick="document.getElementById('audioPlayer').play()">
                        <i class="fas fa-play"></i> Play Audio
                    </button>
                </div>
            `;
        } else {
            messageDiv.textContent = text;
        }

        // Remove welcome message if it exists
        const welcomeMessage = this.conversationDisplay.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.conversationDisplay.appendChild(messageDiv);
        this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;
    }

    extractLeadInfo(userMessage, aiResponse) {
        // Simple lead information extraction
        // In a real implementation, this would be more sophisticated
        // aiResponse could be used for more advanced extraction in the future
        
        const nameMatch = userMessage.match(/my name is (\w+)/i) || userMessage.match(/i'm (\w+)/i);
        if (nameMatch) {
            this.leadData.name = nameMatch[1];
        }
        
        const emailMatch = userMessage.match(/[\w.-]+@[\w.-]+\.\w+/);
        if (emailMatch) {
            this.leadData.email = emailMatch[0];
        }
        
        const phoneMatch = userMessage.match(/(\d{3}[-.]?\d{3}[-.]?\d{4})/);
        if (phoneMatch) {
            this.leadData.phone = phoneMatch[1];
        }
        
        // Update lead display
        this.updateLeadDisplay();
    }

    updateLeadDisplay() {
        if (Object.keys(this.leadData).length > 0) {
            let leadHtml = '<div class="lead-details">';
            
            if (this.leadData.name) {
                leadHtml += `<p><strong>Name:</strong> ${this.leadData.name}</p>`;
            }
            if (this.leadData.email) {
                leadHtml += `<p><strong>Email:</strong> ${this.leadData.email}</p>`;
            }
            if (this.leadData.phone) {
                leadHtml += `<p><strong>Phone:</strong> ${this.leadData.phone}</p>`;
            }
            
            leadHtml += '</div>';
            this.leadInfo.innerHTML = leadHtml;
            
            this.saveLeadBtn.disabled = false;
            this.exportLeadBtn.disabled = false;
        }
    }

    updateUI(state) {
        console.log('🎨 Updating UI to state:', state);
        switch (state) {
            case 'listening':
                this.micButton.classList.add('listening');
                this.micButton.classList.remove('recording');
                this.micIcon.className = 'fas fa-microphone-slash';
                this.micStatus.textContent = 'Voice ON - Click to turn OFF';
                this.stopButton.style.display = 'flex';
                this.aiSpeakingIndicator.style.display = 'none';
                console.log('🎨 UI set to LISTENING mode (blue button)');
                break;
            case 'recording':
                this.micButton.classList.add('recording');
                this.micButton.classList.remove('listening');
                this.micIcon.className = 'fas fa-stop';
                this.micStatus.textContent = 'Recording...';
                this.stopButton.style.display = 'flex';
                this.aiSpeakingIndicator.style.display = 'none';
                console.log('🎨 UI set to RECORDING mode (green button)');
                break;
            case 'processing':
                this.micButton.classList.remove('recording', 'listening');
                this.micIcon.className = 'fas fa-spinner fa-spin';
                this.micStatus.textContent = 'Processing...';
                this.stopButton.style.display = 'flex';
                this.aiSpeakingIndicator.style.display = 'none';
                break;
            case 'speaking':
                this.micButton.classList.remove('recording');
                if (this.isListening) {
                    this.micButton.classList.add('listening');
                    this.micIcon.className = 'fas fa-microphone-slash';
                    this.micStatus.textContent = 'Voice ON - AI speaking...';
                } else {
                    this.micIcon.className = 'fas fa-microphone';
                    this.micStatus.textContent = 'AI is speaking...';
                }
                this.stopButton.style.display = 'flex';
                this.aiSpeakingIndicator.style.display = 'flex';
                break;
            case 'ready':
                this.micButton.classList.remove('recording', 'listening');
                this.micIcon.className = 'fas fa-microphone';
                this.micStatus.textContent = 'Click to turn voice ON';
                this.stopButton.style.display = 'none';
                this.aiSpeakingIndicator.style.display = 'none';
                console.log('🎨 UI set to READY mode (gray button)');
                break;
        }
    }

    updateStatus(message, isError = false) {
        const statusText = this.statusIndicator.querySelector('.status-text');
        statusText.textContent = message;

        // Clear any existing hide timer
        if (this.statusHideTimer) {
            clearTimeout(this.statusHideTimer);
        }

        // Show the status indicator
        this.statusIndicator.classList.remove('hidden');

        // Add error styling if it's an error
        if (isError) {
            this.statusIndicator.style.background = 'rgba(220, 53, 69, 0.9)';
            statusText.style.color = '#ffffff';

            // Hide error status after 3 seconds
            this.statusHideTimer = setTimeout(() => {
                this.statusIndicator.classList.add('hidden');
            }, 3000);
        } else {
            this.statusIndicator.style.background = 'rgba(0, 0, 0, 0.7)';
            statusText.style.color = '#00d4aa';

            // Hide normal status after 2 seconds
            this.statusHideTimer = setTimeout(() => {
                this.statusIndicator.classList.add('hidden');
            }, 2000);
        }
    }

    showError(title, message, details = null) {
        // Create error message in conversation
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message error-message';
        errorDiv.innerHTML = `
            <div class="error-content">
                <strong>❌ ${title}</strong>
                <p>${message}</p>
                ${details ? `<details><summary>Technical Details</summary><pre>${details}</pre></details>` : ''}
            </div>
        `;

        // Remove welcome message if it exists
        const welcomeMessage = this.conversationDisplay.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }

        this.conversationDisplay.appendChild(errorDiv);
        this.conversationDisplay.scrollTop = this.conversationDisplay.scrollHeight;

        // Also update status
        this.updateStatus(`Error: ${title}`, true);
    }

    playLastResponse() {
        if (this.currentAudio) {
            this.audioPlayer.play();
            this.stopButton.disabled = false;
        }
    }

    stopAudio() {
        this.audioPlayer.pause();
        this.audioPlayer.currentTime = 0;
        this.oldStopButton.disabled = true;
    }

    stopEverything() {
        // Stop voice mode if active
        if (this.isListening) {
            this.stopVoiceMode();
        }

        // Stop recording if active
        if (this.isRecording) {
            this.stopRecording();
        }

        // Stop audio playback
        this.audioPlayer.pause();
        this.audioPlayer.currentTime = 0;

        // Reset UI to ready state
        this.updateUI('ready');
        this.updateStatus('Stopped');
    }

    async saveLead() {
        try {
            const response = await fetch('/api/save-lead', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.leadData)
            });
            
            if (response.ok) {
                this.updateStatus('Lead saved successfully');
            } else {
                this.updateStatus('Error saving lead');
            }
        } catch (error) {
            console.error('Save lead error:', error);
            this.updateStatus('Error saving lead');
        }
    }

    exportLead() {
        const leadText = JSON.stringify(this.leadData, null, 2);
        const blob = new Blob([leadText], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `lead_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new VoiceLeadApp();
});
