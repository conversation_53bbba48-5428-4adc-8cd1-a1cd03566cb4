const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const OpenAI = require('openai');
const { STOCK_BUSINESS_SYSTEM_PROMPT, CONVERSATION_STARTERS, MARKET_INSIGHTS } = require('./prompts');
require('dotenv').config();

// Mock mode for testing without API calls
const MOCK_MODE = process.env.MOCK_MODE === 'true';
console.log('Mock mode:', MOCK_MODE ? 'ENABLED' : 'DISABLED');

// Load cleaned conversation data
function loadCleanedConversations() {
  try {
    const conversationData = fs.readFileSync('./Collective Chat.json', 'utf8');
    const conversations = JSON.parse(conversationData);

    // Format conversations for AI reference with stronger instructions
    let formattedConversations = '\n\n## IMPORTANT: ACTUAL MARKET CONVERSATIONS TO REFERENCE\n\n';
    formattedConversations += 'WHEN USERS ASK ABOUT MARKET DISCUSSIONS, STOCKS, OR INVESTMENT CONVERSATIONS, YOU MUST REFERENCE THESE SPECIFIC CONVERSATIONS BELOW:\n\n';

    conversations.forEach((conv, index) => {
      formattedConversations += `**${conv.timestamp}** - ${conv.sender}: "${conv.message}"\n\n`;
    });

    formattedConversations += '\n🔥 CRITICAL INSTRUCTION: When users ask "what was discussed", "what stocks were mentioned", "what did [person] say", etc., you MUST quote directly from these conversations above. Do not give generic responses - use the actual conversation data provided.\n\n';
    formattedConversations += 'Examples of how to respond:\n';
    formattedConversations += '- "GR mentioned that valuations went from $18bn to $170bn over 16 months"\n';
    formattedConversations += '- "Simon Brewer discussed shorting the S&P, targeting a 5-10% drawdown"\n';
    formattedConversations += '- "There were discussions about Apple, Tesla, Spotify, BP, and other specific companies"\n\n';

    return formattedConversations;
  } catch (error) {
    console.log('Could not load cleaned conversations:', error.message);
    return '';
  }
}

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Conversation history functions
async function saveConversationExchange(userMessage, aiResponse, leadData) {
  try {
    const conversationFile = path.join(__dirname, 'conversation_history.json');
    let conversations = [];

    // Read existing conversations if file exists
    if (fs.existsSync(conversationFile)) {
      try {
        const data = fs.readFileSync(conversationFile, 'utf8');
        conversations = JSON.parse(data);
      } catch (error) {
        console.error('Error reading conversation history:', error);
      }
    }

    // Add new conversation exchange
    const exchange = {
      timestamp: new Date().toISOString(),
      userMessage,
      aiResponse,
      leadData: leadData || {},
      sessionId: Date.now().toString() // Simple session tracking
    };

    conversations.push(exchange);

    // Keep only last 100 exchanges to prevent file from getting too large
    if (conversations.length > 100) {
      conversations = conversations.slice(-100);
    }

    // Save back to file
    fs.writeFileSync(conversationFile, JSON.stringify(conversations, null, 2));
  } catch (error) {
    console.error('Error saving conversation:', error);
  }
}

async function getConversationSummary() {
  try {
    const conversationFile = path.join(__dirname, 'conversation_history.json');

    if (!fs.existsSync(conversationFile)) {
      return "I don't have any previous conversation history to summarize yet. This appears to be our first interaction!";
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const conversations = JSON.parse(data);

    if (conversations.length === 0) {
      return "I don't have any previous conversation history to summarize yet.";
    }

    // Get conversations from the last week
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentConversations = conversations.filter(conv =>
      new Date(conv.timestamp) > oneWeekAgo
    );

    if (recentConversations.length === 0) {
      return "I don't have any conversations from the last week to summarize.";
    }

    // Create summary
    const leadInfo = recentConversations
      .map(conv => conv.leadData)
      .filter(data => data && Object.keys(data).length > 0)
      .pop(); // Get the most recent lead data

    const keyTopics = recentConversations
      .map(conv => conv.userMessage)
      .join(' ')
      .toLowerCase();

    let summary = `Here's a summary of our recent conversations:\n\n`;
    summary += `📅 **Time Period**: Last week (${recentConversations.length} exchanges)\n\n`;

    if (leadInfo && Object.keys(leadInfo).length > 0) {
      summary += `👤 **Lead Information Collected**:\n`;
      if (leadInfo.name) summary += `- Name: ${leadInfo.name}\n`;
      if (leadInfo.email) summary += `- Email: ${leadInfo.email}\n`;
      if (leadInfo.phone) summary += `- Phone: ${leadInfo.phone}\n`;
      summary += `\n`;
    }

    summary += `💬 **Key Discussion Topics**:\n`;
    if (keyTopics.includes('invest') || keyTopics.includes('stock')) {
      summary += `- Investment interests and stock market discussions\n`;
    }
    if (keyTopics.includes('portfolio') || keyTopics.includes('manage')) {
      summary += `- Portfolio management and investment strategies\n`;
    }
    if (keyTopics.includes('beginner') || keyTopics.includes('new')) {
      summary += `- Beginner investment guidance and education\n`;
    }
    if (keyTopics.includes('advisor') || keyTopics.includes('consultation')) {
      summary += `- Financial advisor services and consultation scheduling\n`;
    }

    summary += `\n📊 **Next Steps**: Based on our conversations, I'd recommend scheduling a consultation to discuss your investment goals in more detail.`;

    return summary;

  } catch (error) {
    console.error('Error getting conversation summary:', error);
    return "I'm having trouble accessing the conversation history right now, but I'm here to help with any investment questions you have!";
  }
}

async function getPreviousConversationContext(leadData) {
  try {
    const conversationFile = path.join(__dirname, 'conversation_history.json');

    if (!fs.existsSync(conversationFile)) {
      return null;
    }

    const data = fs.readFileSync(conversationFile, 'utf8');
    const conversations = JSON.parse(data);

    if (conversations.length === 0) {
      return null;
    }

    // Try to find conversations for this specific lead
    let relevantConversations = [];

    if (leadData && (leadData.email || leadData.phone || leadData.name)) {
      // Find conversations with matching lead data
      relevantConversations = conversations.filter(conv => {
        const convLead = conv.leadData || {};
        return (leadData.email && convLead.email === leadData.email) ||
               (leadData.phone && convLead.phone === leadData.phone) ||
               (leadData.name && convLead.name === leadData.name);
      });
    }

    // If no specific lead match, get recent conversations (last 24 hours)
    if (relevantConversations.length === 0) {
      const oneDayAgo = new Date();
      oneDayAgo.setDate(oneDayAgo.getDate() - 1);

      relevantConversations = conversations.filter(conv =>
        new Date(conv.timestamp) > oneDayAgo
      ).slice(-5); // Last 5 exchanges
    }

    if (relevantConversations.length === 0) {
      return null;
    }

    // Create context summary
    let context = "Previous conversation context:\n";

    const lastLead = relevantConversations
      .map(conv => conv.leadData)
      .filter(data => data && Object.keys(data).length > 0)
      .pop();

    if (lastLead && Object.keys(lastLead).length > 0) {
      context += `Lead info: ${JSON.stringify(lastLead)}\n`;
    }

    // Add key conversation points
    const recentExchanges = relevantConversations.slice(-3);
    context += "Recent discussion points:\n";
    recentExchanges.forEach(conv => {
      context += `User: ${conv.userMessage.substring(0, 100)}...\n`;
      context += `You: ${conv.aiResponse.substring(0, 100)}...\n`;
    });

    return context;

  } catch (error) {
    console.error('Error getting previous conversation context:', error);
    return null;
  }
}

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Configure multer for file uploads (audio files)
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 25 * 1024 * 1024 // 25MB limit for audio files
  }
});

// Serve static files
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API Routes
app.post('/api/speech-to-text', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    console.log('Received audio file:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      filename: req.file.filename
    });

    // Determine the correct file extension for OpenAI
    let fileExtension = '.wav'; // Default to WAV
    if (req.file.mimetype.includes('mp4')) {
      fileExtension = '.mp4';
    } else if (req.file.mimetype.includes('mpeg')) {
      fileExtension = '.mp3';
    } else if (req.file.mimetype.includes('ogg')) {
      fileExtension = '.ogg';
    } else if (req.file.mimetype.includes('webm')) {
      fileExtension = '.webm';
    }

    // Create a new filename with proper extension
    const newFilePath = req.file.path + fileExtension;
    fs.renameSync(req.file.path, newFilePath);

    // Create a readable stream from the renamed file
    const audioStream = fs.createReadStream(newFilePath);

    // Use OpenAI Whisper for speech-to-text with retry logic or mock mode
    let transcription;

    if (MOCK_MODE) {
      console.log('Mock mode: Simulating speech transcription...');
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock transcriptions for testing
      const mockTranscriptions = [
        "Hello, I'm interested in learning about stock investments.",
        "What are the current market trends?",
        "Can you tell me about dividend stocks?",
        "I'd like to know about portfolio diversification.",
        "What's your recommendation for a beginner investor?"
      ];

      transcription = {
        text: mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)]
      };

      console.log('Mock transcription:', transcription.text);
    } else {
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          console.log(`Attempting transcription (attempt ${retryCount + 1}/${maxRetries})...`);

          transcription = await openai.audio.transcriptions.create({
            file: audioStream,
            model: 'whisper-1',
            language: 'en'
          });

          console.log('Transcription successful:', transcription.text);
          break; // Success, exit retry loop

        } catch (error) {
          retryCount++;
          console.error(`Transcription attempt ${retryCount} failed:`, error.message);

          if (retryCount >= maxRetries) {
            throw error; // Re-throw after max retries
          }

          // Wait before retrying (exponential backoff)
          const waitTime = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
          console.log(`Waiting ${waitTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));

          // Create a new stream for retry
          audioStream.destroy();
          audioStream = fs.createReadStream(newFilePath);
        }
      }
    }

    // Clean up the renamed file
    fs.unlinkSync(newFilePath);

    // File already cleaned up above

    res.json({
      transcription: transcription.text,
      success: true
    });

  } catch (error) {
    console.error('Speech-to-text error:', error);

    // Clean up file if it exists (check both original and renamed paths)
    if (req.file) {
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      const newFilePath = req.file.path + (req.file.mimetype.includes('webm') ? '.webm' : '.wav');
      if (fs.existsSync(newFilePath)) {
        fs.unlinkSync(newFilePath);
      }
    }

    // Provide more specific error messages
    let errorMessage = 'Failed to transcribe audio';
    let details = error.message;

    if (error.code === 'ECONNRESET' || error.message.includes('Connection error')) {
      errorMessage = 'Network connection error';
      details = 'Unable to connect to OpenAI API. Please check your internet connection and try again.';
    } else if (error.status === 401) {
      errorMessage = 'Authentication error';
      details = 'Invalid API key. Please check your OpenAI API configuration.';
    } else if (error.status === 429) {
      errorMessage = 'Rate limit exceeded';
      details = 'Too many requests. Please wait a moment and try again.';
    } else if (error.status === 413) {
      errorMessage = 'File too large';
      details = 'Audio file is too large. Please try a shorter recording.';
    }

    res.status(500).json({
      error: errorMessage,
      details: details,
      retryable: error.code === 'ECONNRESET' || error.message.includes('Connection error')
    });
  }
});

app.post('/api/chat', async (req, res) => {
  try {
    const { message, context, leadData, conversationHistory } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'No message provided' });
    }

    // Check if user is asking about conversation history
    const historyKeywords = ['what was discussed', 'last week', 'previous conversation', 'chat history', 'what did we talk about'];
    const isHistoryRequest = historyKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );

    if (isHistoryRequest) {
      // Load and summarize conversation history
      const conversationSummary = await getConversationSummary();
      return res.json({
        response: conversationSummary,
        success: true
      });
    }

    // Build conversation context
    let systemPrompt = STOCK_BUSINESS_SYSTEM_PROMPT;

    // Add cleaned conversation data for reference
    const cleanedConversations = loadCleanedConversations();
    if (cleanedConversations) {
      systemPrompt += cleanedConversations;
      console.log('✅ Added conversation data to system prompt');
      console.log('📝 First 500 chars of conversation data:', cleanedConversations.substring(0, 500));
    } else {
      console.log('❌ No conversation data loaded');
    }

    // Load previous conversation context for this lead
    const previousContext = await getPreviousConversationContext(leadData);
    if (previousContext) {
      systemPrompt += `\n\nPrevious conversation context: ${previousContext}`;
    }

    if (leadData && Object.keys(leadData).length > 0) {
      systemPrompt += `\n\nCurrent lead information: ${JSON.stringify(leadData)}`;
    }

    // Add a random market insight occasionally to provide value
    if (Math.random() < 0.3) {
      const insight = MARKET_INSIGHTS[Math.floor(Math.random() * MARKET_INSIGHTS.length)];
      systemPrompt += `\n\nConsider sharing this insight if relevant: "${insight}"`;
    }



    // Debug: Log final system prompt details
    console.log('🔍 Final system prompt length:', systemPrompt.length);
    console.log('🔍 Contains GR messages:', systemPrompt.includes('GR'));
    console.log('🔍 Contains Valuation:', systemPrompt.includes('Valuation'));
    console.log('🔍 Last 200 chars of system prompt:', systemPrompt.slice(-200));

    // Build messages array with conversation history
    const messages = [{ role: 'system', content: systemPrompt }];

    // Add conversation history if provided
    if (conversationHistory && Array.isArray(conversationHistory)) {
      messages.push(...conversationHistory);
    }

    // Add current user message
    messages.push({ role: 'user', content: message });

    let completion;

    if (MOCK_MODE) {
      console.log('Mock mode: Simulating AI chat response...');
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate processing time

      // Mock responses based on message content
      let mockResponse = "Thank you for your interest in stock investments. I'd be happy to help you learn more about the market.";

      if (message.toLowerCase().includes('market') || message.toLowerCase().includes('trend')) {
        mockResponse = "The current market shows mixed signals. Technology stocks have been performing well, while traditional sectors are showing stability. Would you like to discuss specific sectors?";
      } else if (message.toLowerCase().includes('dividend')) {
        mockResponse = "Dividend stocks can provide steady income. Companies like Johnson & Johnson and Coca-Cola have strong dividend histories. What's your investment timeline?";
      } else if (message.toLowerCase().includes('beginner') || message.toLowerCase().includes('start')) {
        mockResponse = "For beginners, I recommend starting with diversified index funds and gradually learning about individual stocks. What's your risk tolerance?";
      } else if (message.toLowerCase().includes('portfolio')) {
        mockResponse = "Portfolio diversification is key to managing risk. A good mix might include 60% stocks, 30% bonds, and 10% alternatives. What's your current allocation?";
      }

      completion = {
        choices: [{
          message: {
            content: mockResponse
          }
        }]
      };

      console.log('Mock response:', mockResponse);
    } else {
      completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: messages,
        max_tokens: 200,
        temperature: 0.7,
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      });
    }

    const response = completion.choices[0].message.content;

    // Save conversation to history
    await saveConversationExchange(message, response, leadData);

    res.json({
      response,
      success: true
    });

  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      error: 'Failed to get AI response',
      details: error.message
    });
  }
});

app.post('/api/text-to-speech', async (req, res) => {
  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'No text provided' });
    }

    let buffer;

    if (MOCK_MODE) {
      console.log('Mock mode: Simulating text-to-speech...');
      // Create a minimal MP3 header for a silent audio file
      // This creates a very short silent MP3 that browsers can play
      const silentMp3 = Buffer.from([
        0xFF, 0xFB, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
      ]);
      buffer = silentMp3;
      console.log('Mock TTS: Generated silent audio file');
    } else {
      // Use OpenAI TTS to convert text to speech
      const mp3 = await openai.audio.speech.create({
        model: 'tts-1',
        voice: 'alloy', // Professional, clear voice
        input: text,
        speed: 1.0
      });

      // Convert the response to a buffer
      buffer = Buffer.from(await mp3.arrayBuffer());
    }

    // Set appropriate headers for audio response
    res.set({
      'Content-Type': 'audio/mpeg',
      'Content-Length': buffer.length,
      'Cache-Control': 'no-cache'
    });

    res.send(buffer);

  } catch (error) {
    console.error('Text-to-speech error:', error);
    res.status(500).json({
      error: 'Failed to generate speech',
      details: error.message
    });
  }
});

app.post('/api/save-lead', async (req, res) => {
  try {
    const leadData = req.body;

    // Add timestamp
    leadData.timestamp = new Date().toISOString();
    leadData.id = Date.now().toString();

    // In a real application, you would save to a database
    // For now, we'll save to a JSON file
    const leadsFile = path.join(__dirname, 'leads.json');
    let leads = [];

    // Read existing leads if file exists
    if (fs.existsSync(leadsFile)) {
      try {
        const data = fs.readFileSync(leadsFile, 'utf8');
        leads = JSON.parse(data);
      } catch (error) {
        console.error('Error reading leads file:', error);
      }
    }

    // Add new lead
    leads.push(leadData);

    // Save back to file
    fs.writeFileSync(leadsFile, JSON.stringify(leads, null, 2));

    res.json({
      success: true,
      message: 'Lead saved successfully',
      leadId: leadData.id
    });

  } catch (error) {
    console.error('Save lead error:', error);
    res.status(500).json({
      error: 'Failed to save lead',
      details: error.message
    });
  }
});

// app.listen(PORT, () => {
//   console.log(`Echo Voice Leads server running on port ${PORT}`);
//   console.log(`Open http://localhost:${PORT} to view the application`);
// });
app.listen(PORT, '0.0.0.0', () => {
  const os = require('os');
  const networkInterfaces = os.networkInterfaces();
  let networkIP = 'localhost';

  // Find the first non-internal IPv4 address
  for (const interfaceName in networkInterfaces) {
    const addresses = networkInterfaces[interfaceName];
    for (const address of addresses) {
      if (address.family === 'IPv4' && !address.internal) {
        networkIP = address.address;
        break;
      }
    }
    if (networkIP !== 'localhost') break;
  }

  console.log(`Echo Voice Leads server running on port ${PORT}`);
  console.log(`Local:   http://localhost:${PORT}`);
  console.log(`Network: http://${networkIP}:${PORT}`);
  console.log(
    `\nAccess from other devices on the same WiFi using the Network URL`
  );
});
